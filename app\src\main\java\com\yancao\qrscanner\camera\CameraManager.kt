package com.yancao.qrscanner.camera

import android.content.Context
import android.graphics.ImageFormat
import android.hardware.camera2.CameraCharacteristics
import android.util.Log
import android.util.Size
import androidx.annotation.OptIn
import androidx.camera.camera2.interop.Camera2CameraInfo
import androidx.camera.camera2.interop.ExperimentalCamera2Interop
import androidx.camera.core.Camera
import androidx.camera.core.CameraControl
import androidx.camera.core.CameraInfo
import androidx.camera.core.CameraSelector
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.core.UseCase
import androidx.camera.core.resolutionselector.ResolutionSelector
import androidx.camera.core.resolutionselector.ResolutionStrategy
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.google.mlkit.vision.barcode.common.Barcode
import java.util.concurrent.Executors
import kotlin.math.abs


/**
 * 相机管理器 - 负责管理CameraX相关功能
 *
 * 功能包括：
 * 1. 基础相机预览和图像分析
 * 2. 闪光灯控制
 * 3. 焦段缩放控制
 * 4. 实时二维码扫描
 * 5. 主摄和广角镜头切换支持
 */
@ExperimentalGetImage
class CameraManager(private val context: Context) {

    private var cameraProvider: ProcessCameraProvider? = null
    private val cameraExecutor = Executors.newSingleThreadExecutor()

    // 图像分析器用于实时二维码扫描
    private var imageAnalyzer: ImageAnalysis? = null
    private var qrAnalyzer: RealtimeQRAnalyzer? = null

    // 相机控制相关
    private var camera: Camera? = null
    private var cameraControl: CameraControl? = null
    private var cameraInfo: CameraInfo? = null

    // 新增：镜头类型枚举
    enum class LensType {
        MAIN,       // 主摄
        WIDE_ANGLE  // 广角
    }

    // 新增：当前使用的镜头类型
    private var currentLensType = LensType.MAIN

    // 新增：可用的镜头列表
    private var availableLenses = mutableListOf<LensType>()

    /**
     * 获取当前镜头类型
     */
    fun getCurrentLensType(): LensType = currentLensType

    /**
     * 获取可用的镜头列表
     */
    fun getAvailableLenses(): List<LensType> = availableLenses.toList()

    /**
     * 检查是否支持广角镜头
     */
    @OptIn(ExperimentalCamera2Interop::class)
    fun checkAvailableLenses() {
        availableLenses.clear()

        try {
            val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
            val provider = cameraProviderFuture.get()

            // 检查主摄（标准后置摄像头）
            if (provider.hasCamera(CameraSelector.DEFAULT_BACK_CAMERA)) {
                availableLenses.add(LensType.MAIN)
                Log.d("CameraManager", "主摄可用")
            }

            // 检查广角镜头
            // 方法1：通过焦距检查广角镜头
            val backCameras = provider.availableCameraInfos.filter { cameraInfo ->
                val camera2Info = Camera2CameraInfo.from(cameraInfo)
                val characteristics = camera2Info.getCameraCharacteristic(CameraCharacteristics.LENS_FACING)
                characteristics == CameraCharacteristics.LENS_FACING_BACK
            }

            // 查找广角镜头（通常焦距较小）
            var hasWideAngle = false
            for (cameraInfo in backCameras) {
                try {
                    val camera2Info = Camera2CameraInfo.from(cameraInfo)
                    val focalLengths = camera2Info.getCameraCharacteristic(
                        CameraCharacteristics.LENS_INFO_AVAILABLE_FOCAL_LENGTHS
                    )

                    focalLengths?.let { lengths ->
                        // 广角镜头通常焦距较小（< 3.0mm）
                        val hasWideAngleFocal = lengths.any { it < 3.0f }
                        if (hasWideAngleFocal && !hasWideAngle) {
                            hasWideAngle = true
                            Log.d("CameraManager", "发现广角镜头，焦距: ${lengths.contentToString()}")
                        }
                    }
                } catch (e: Exception) {
                    Log.w("CameraManager", "检查镜头焦距失败: ${e.message}")
                }
            }

            // 方法2：通过CameraSelector.Builder构建广角选择器
            if (!hasWideAngle) {
                try {
                    val wideAngleSelector = CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                        .build()

                    // 尝试获取所有后置摄像头
                    val availableCameras = provider.availableCameraInfos.filter { cameraInfo ->
                        wideAngleSelector.filter(listOf(cameraInfo)).isNotEmpty()
                    }

                    // 如果有多个后置摄像头，很可能其中包含广角
                    if (availableCameras.size > 1) {
                        hasWideAngle = true
                        Log.d("CameraManager", "检测到多个后置摄像头，可能包含广角镜头")
                    }
                } catch (e: Exception) {
                    Log.w("CameraManager", "广角镜头检测失败: ${e.message}")
                }
            }

            if (hasWideAngle) {
                availableLenses.add(LensType.WIDE_ANGLE)
                Log.d("CameraManager", "广角镜头可用")
            }

        } catch (e: Exception) {
            Log.e("CameraManager", "检查可用镜头失败", e)
            // 默认至少支持主摄
            if (availableLenses.isEmpty()) {
                availableLenses.add(LensType.MAIN)
            }
        }

        Log.d("CameraManager", "可用镜头: ${availableLenses.joinToString(", ")}")
    }

    /**
     * 根据镜头类型创建CameraSelector
     */
    @OptIn(ExperimentalCamera2Interop::class)
    private fun createCameraSelector(lensType: LensType): CameraSelector {
        return when (lensType) {
            LensType.MAIN -> {
                // 主摄：使用默认后置摄像头
                CameraSelector.DEFAULT_BACK_CAMERA
            }
            LensType.WIDE_ANGLE -> {
                // 广角：尝试选择焦距最小的后置摄像头
                CameraSelector.Builder()
                    .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                    .addCameraFilter { cameraInfos ->
                        // 尝试根据焦距选择广角镜头
                        val sortedCameras = cameraInfos.sortedBy { cameraInfo ->
                            try {
                                val camera2Info = Camera2CameraInfo.from(cameraInfo)
                                val focalLengths = camera2Info.getCameraCharacteristic(
                                    CameraCharacteristics.LENS_INFO_AVAILABLE_FOCAL_LENGTHS
                                )
                                focalLengths?.minOrNull() ?: Float.MAX_VALUE
                            } catch (e: Exception) {
                                Float.MAX_VALUE
                            }
                        }

                        // 返回焦距最小的摄像头（通常是广角）
                        if (sortedCameras.size > 1) {
                            listOf(sortedCameras.first())
                        } else {
                            sortedCameras
                        }
                    }
                    .build()
            }
        }
    }

    /**
     * 切换镜头类型
     */
    fun switchLens(
        newLensType: LensType,
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        enableRealtimeScanning: Boolean = false,
        onQRCodeDetected: ((List<String>) -> Unit)? = null,
        onQRCodeWithPosition: ((List<Barcode>, Int, Int) -> Unit)? = null
    ): Boolean {
        if (!availableLenses.contains(newLensType)) {
            Log.w("CameraManager", "请求的镜头类型不可用: $newLensType")
            return false
        }

        if (currentLensType == newLensType) {
            Log.d("CameraManager", "已经在使用请求的镜头类型: $newLensType")
            return true
        }

        currentLensType = newLensType
        Log.d("CameraManager", "切换到镜头类型: $newLensType")

        // 重新启动相机
        return startCameraInternal(
            previewView,
            lifecycleOwner,
            enableRealtimeScanning,
            onQRCodeDetected,
            onQRCodeWithPosition,
            newLensType
        )
    }

    @OptIn(ExperimentalCamera2Interop::class)
    @ExperimentalGetImage
    fun startCamera(
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        enableRealtimeScanning: Boolean = false,
        onQRCodeDetected: ((List<String>) -> Unit)? = null,
        onQRCodeWithPosition: ((List<Barcode>, Int, Int) -> Unit)? = null
    ): Boolean {
        // 首次启动时检查可用镜头
        if (availableLenses.isEmpty()) {
            checkAvailableLenses()
        }

        return startCameraInternal(
            previewView,
            lifecycleOwner,
            enableRealtimeScanning,
            onQRCodeDetected,
            onQRCodeWithPosition,
            currentLensType
        )
    }

    @OptIn(ExperimentalCamera2Interop::class)
    @ExperimentalGetImage
    private fun startCameraInternal(
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        enableRealtimeScanning: Boolean = false,
        onQRCodeDetected: ((List<String>) -> Unit)? = null,
        onQRCodeWithPosition: ((List<Barcode>, Int, Int) -> Unit)? = null,
        lensType: LensType
    ): Boolean {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()

                // 根据镜头类型调整分辨率策略
                val analysisResolution = when (lensType) {
                    LensType.MAIN -> Size(1152, 2016)      // 主摄使用较高分辨率
                    LensType.WIDE_ANGLE -> Size(1080, 1920) // 广角使用适中分辨率
                }

                val resolutionSelector = ResolutionSelector.Builder()
                    .setResolutionStrategy(
                        ResolutionStrategy(
                            analysisResolution,
                            ResolutionStrategy.FALLBACK_RULE_CLOSEST_LOWER_THEN_HIGHER
                        )
                    ).setResolutionFilter { supportedSizes, rotationDegrees ->
                        val targetSize = analysisResolution

                        val sortedSizes = supportedSizes.sortedBy { size ->
                            val actualWidth = if (rotationDegrees == 90 || rotationDegrees == 270) {
                                size.height
                            } else {
                                size.width
                            }
                            val actualHeight = if (rotationDegrees == 90 || rotationDegrees == 270) {
                                size.width
                            } else {
                                size.height
                            }

                            val widthDiff = abs(actualWidth - targetSize.width)
                            val heightDiff = abs(actualHeight - targetSize.height)
                            val totalDiff = widthDiff + heightDiff

                            Log.d("CameraManager", "[$lensType] 分辨率 ${size.width}x${size.height} -> 旋转后 ${actualWidth}x${actualHeight}, 差异: $totalDiff")

                            totalDiff
                        }

                        val bestChoices = sortedSizes.take(3)
                        Log.d("CameraManager", "[$lensType] 选择的分辨率: ${bestChoices.map { "${it.width}x${it.height}" }}")

                        bestChoices.ifEmpty { supportedSizes }
                    }
                    .build()

                // Preview配置
                val preview = Preview.Builder()
                    .setResolutionSelector(resolutionSelector)
                    .build()
                    .also {
                        it.surfaceProvider = previewView.surfaceProvider
                    }

                // 创建用例列表
                val useCases = mutableListOf<UseCase>(preview)

                // 如果启用实时扫描，创建图像分析器
                if (enableRealtimeScanning) {
                    qrAnalyzer = RealtimeQRAnalyzer(onQRCodeDetected, onQRCodeWithPosition)
                    imageAnalyzer = ImageAnalysis.Builder()
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .setResolutionSelector(resolutionSelector)
                        .build()
                        .also {
                            it.setAnalyzer(cameraExecutor, qrAnalyzer!!)
                        }
                    useCases.add(imageAnalyzer!!)
                }

                // 创建相机选择器
                val cameraSelector = createCameraSelector(lensType)

                // 绑定用例到相机
                cameraProvider?.unbindAll()
                camera = cameraProvider?.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    *useCases.toTypedArray()
                )

                // 获取相机控制对象
                camera?.let {
                    cameraControl = it.cameraControl
                    cameraInfo = it.cameraInfo

                    // 记录当前镜头信息
                    logCurrentCameraInfo(lensType)
                }

                // 检查实际分辨率
                imageAnalyzer?.let { analyzer ->
                    Log.d("CameraManager", "[$lensType] ImageAnalysis实际分辨率: ${analyzer.resolutionInfo}")
                }

                Log.d("CameraManager", "相机启动成功，镜头类型: $lensType")
                return@addListener

            } catch (e: Exception) {
                Log.e("CameraManager", "相机启动失败", e)
            }

        }, ContextCompat.getMainExecutor(context))

        return true
    }

    /**
     * 记录当前相机信息
     */
    @OptIn(ExperimentalCamera2Interop::class)
    private fun logCurrentCameraInfo(lensType: LensType) {
        try {
            cameraInfo?.let { info ->
                val camera2Info = Camera2CameraInfo.from(info)
                val focalLengths = camera2Info.getCameraCharacteristic(
                    CameraCharacteristics.LENS_INFO_AVAILABLE_FOCAL_LENGTHS
                )
                val sensorSize = camera2Info.getCameraCharacteristic(
                    CameraCharacteristics.SENSOR_INFO_PHYSICAL_SIZE
                )

                Log.d("CameraManager", "[$lensType] 当前相机信息:")
                Log.d("CameraManager", "  焦距: ${focalLengths?.contentToString()}")
                Log.d("CameraManager", "  传感器尺寸: $sensorSize")
                Log.d("CameraManager", "  缩放范围: ${getMinZoomRatio()} - ${getMaxZoomRatio()}")
                Log.d("CameraManager", "  支持闪光灯: ${hasFlashlight()}")
            }
        } catch (e: Exception) {
            Log.w("CameraManager", "获取相机信息失败: ${e.message}")
        }
    }

    /**
     * 设置闪光灯状态
     */
    fun setFlashlight(enabled: Boolean): Boolean {
        return try {
            cameraControl?.enableTorch(enabled)
            Log.d("CameraManager", "闪光灯${if (enabled) "开启" else "关闭"}成功")
            true
        } catch (e: Exception) {
            Log.e("CameraManager", "闪光灯控制失败", e)
            false
        }
    }

    /**
     * 检查是否支持闪光灯
     */
    fun hasFlashlight(): Boolean {
        return cameraInfo?.hasFlashUnit() ?: false
    }

    /**
     * 设置缩放比例
     */
    fun setZoomRatio(zoomRatio: Float): Boolean {
        return try {
            val clampedRatio = zoomRatio.coerceIn(getMinZoomRatio(), getMaxZoomRatio())
            cameraControl?.setZoomRatio(clampedRatio)
            Log.d("CameraManager", "设置缩放比例: $clampedRatio")
            true
        } catch (e: Exception) {
            Log.e("CameraManager", "缩放控制失败", e)
            false
        }
    }

    /**
     * 获取最小缩放比例
     */
    fun getMinZoomRatio(): Float {
        return cameraInfo?.zoomState?.value?.minZoomRatio ?: 1.0f
    }

    /**
     * 获取最大缩放比例
     */
    fun getMaxZoomRatio(): Float {
        return cameraInfo?.zoomState?.value?.maxZoomRatio ?: 1.0f
    }

    /**
     * 获取当前缩放比例
     */
    fun getCurrentZoomRatio(): Float {
        return cameraInfo?.zoomState?.value?.zoomRatio ?: 1.0f
    }

    fun shutdown() {
        cameraExecutor.shutdown()
    }
}