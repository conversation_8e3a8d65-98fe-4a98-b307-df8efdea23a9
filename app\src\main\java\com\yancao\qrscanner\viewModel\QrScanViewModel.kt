package com.yancao.qrscanner.viewModel

import android.app.Application
import androidx.annotation.OptIn
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.view.PreviewView
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.google.mlkit.vision.barcode.common.Barcode
import com.yancao.qrscanner.camera.CameraManager
import com.yancao.qrscanner.domain.ScanResultsHolder

/**
 * 二维码扫描的 ViewModel
 * 负责管理摄像头操作、扫描状态和结果数据
 *
 * 功能说明：
 * - 管理摄像头的启动和停止
 * - 控制实时扫描的开启和关闭
 * - 处理拍照功能
 * - 管理扫描结果的存储和获取
 * - 提供二维码位置信息用于绘制绿框
 * - 集成闪光灯和缩放控制功能
 * - 新增：支持主摄和广角镜头切换
 */
@ExperimentalGetImage
class QrScanViewModel(application: Application) : AndroidViewModel(application) {

    private val cameraManager = CameraManager(application)

    // 实时扫描结果的 LiveData
    val realtimeScanResults = MutableLiveData<List<String>>()

    // 扫描状态的 LiveData
    val isScanningEnabled = MutableLiveData(false)

    // Pair 的第一个元素是检测到的二维码列表，第二个元素是图像尺寸 (width, height)
    val qrCodePositions = MutableLiveData<Pair<List<Barcode>, Pair<Int, Int>>>()

    // 相机控制相关的 LiveData
    val isFlashlightEnabled = MutableLiveData(false)
    val zoomRatio = MutableLiveData(1.0f)
    val minZoomRatio = MutableLiveData(1.0f)
    val maxZoomRatio = MutableLiveData(1.0f)
    val hasFlashlight = MutableLiveData(false)

    // 新增：镜头相关的 LiveData
    val currentLensType = MutableLiveData<CameraManager.LensType>(CameraManager.LensType.MAIN)
    val availableLenses = MutableLiveData<List<CameraManager.LensType>>(emptyList())

    /**
     * 启动相机
     */
    @ExperimentalGetImage
    fun startCamera(
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        enableRealtimeScanning: Boolean = false
    ) {
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = enableRealtimeScanning,
            onQRCodeDetected = { detectedCodes ->
                realtimeScanResults.postValue(detectedCodes)
            },
            onQRCodeWithPosition = { barcodes, imageWidth, imageHeight ->
                qrCodePositions.postValue(Pair(barcodes, Pair(imageWidth, imageHeight)))
            }
        )

        // 初始化相机控制相关状态
        initializeCameraControlStates()
    }

    /**
     * 初始化相机控制状态
     */
    private fun initializeCameraControlStates() {
        // 检查闪光灯支持
        hasFlashlight.postValue(cameraManager.hasFlashlight())

        // 初始化缩放范围
        minZoomRatio.postValue(cameraManager.getMinZoomRatio())
        maxZoomRatio.postValue(cameraManager.getMaxZoomRatio())
        zoomRatio.postValue(cameraManager.getCurrentZoomRatio())

        // 新增：初始化镜头信息
        currentLensType.postValue(cameraManager.getCurrentLensType())
        availableLenses.postValue(cameraManager.getAvailableLenses())
    }

    /**
     * 切换镜头类型
     */
    @ExperimentalGetImage
    fun switchLens(
        newLensType: CameraManager.LensType,
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner
    ): Boolean {
        val isCurrentlyScanning = isScanningEnabled.value ?: false

        val success = cameraManager.switchLens(
            newLensType = newLensType,
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = isCurrentlyScanning,
            onQRCodeDetected = if (isCurrentlyScanning) { detectedCodes ->
                realtimeScanResults.postValue(detectedCodes)
            } else null,
            onQRCodeWithPosition = if (isCurrentlyScanning) { barcodes, imageWidth, imageHeight ->
                qrCodePositions.postValue(Pair(barcodes, Pair(imageWidth, imageHeight)))
            } else null
        )

        if (success) {
            // 更新镜头状态
            currentLensType.postValue(newLensType)
            // 重新初始化相机控制状态（因为不同镜头的参数可能不同）
            initializeCameraControlStates()
        }

        return success
    }

    /**
     * 获取可用镜头列表
     */
    fun getAvailableLensTypes(): List<CameraManager.LensType> {
        return cameraManager.getAvailableLenses()
    }

    /**
     * 检查是否支持广角镜头
     */
    fun hasWideAngleLens(): Boolean {
        return cameraManager.getAvailableLenses().contains(CameraManager.LensType.WIDE_ANGLE)
    }

    /**
     * 开始实时扫描
     */
    @ExperimentalGetImage
    fun startRealtimeScanning(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
        isScanningEnabled.postValue(true)

        val currentLens = currentLensType.value ?: CameraManager.LensType.MAIN
        cameraManager.switchLens(
            newLensType = currentLens,
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = true,
            onQRCodeDetected = { detectedCodes ->
                realtimeScanResults.postValue(detectedCodes)
            },
            onQRCodeWithPosition = { barcodes, imageWidth, imageHeight ->
                qrCodePositions.postValue(Pair(barcodes, Pair(imageWidth, imageHeight)))
            }
        )

        // 重新初始化相机控制状态
        initializeCameraControlStates()
    }

    /**
     * 停止实时扫描
     */
    @ExperimentalGetImage
    fun stopRealtimeScanning(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
        isScanningEnabled.postValue(false)

        val currentLens = currentLensType.value ?: CameraManager.LensType.MAIN
        cameraManager.switchLens(
            newLensType = currentLens,
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = false,
            onQRCodeDetected = null,
            onQRCodeWithPosition = null
        )

        // 重新初始化相机控制状态
        initializeCameraControlStates()
    }

    // 闪光灯控制方法
    fun toggleFlashlight(): Boolean {
        val currentState = isFlashlightEnabled.value ?: false
        val newState = !currentState

        return if (cameraManager.setFlashlight(newState)) {
            isFlashlightEnabled.postValue(newState)
            true
        } else {
            false
        }
    }

    // 缩放控制方法
    fun setZoomRatio(ratio: Float): Boolean {
        return if (cameraManager.setZoomRatio(ratio)) {
            zoomRatio.postValue(ratio)
            true
        } else {
            false
        }
    }

    fun zoomIn(step: Float = 0.5f) {
        val currentRatio = zoomRatio.value ?: 1.0f
        val maxRatio = maxZoomRatio.value ?: 1.0f
        val newRatio = (currentRatio + step).coerceAtMost(maxRatio)
        setZoomRatio(newRatio)
    }

    fun zoomOut(step: Float = 0.5f) {
        val currentRatio = zoomRatio.value ?: 1.0f
        val minRatio = minZoomRatio.value ?: 1.0f
        val newRatio = (currentRatio - step).coerceAtLeast(minRatio)
        setZoomRatio(newRatio)
    }

    fun resetZoom() {
        setZoomRatio(1.0f)
    }

    // 扫描结果管理方法
    fun getAllScanResults(): List<String> {
        return ScanResultsHolder.scanResults
    }

    fun clearScanResults() {
        ScanResultsHolder.clearResults()
        realtimeScanResults.postValue(emptyList())
        qrCodePositions.postValue(Pair(emptyList(), Pair(0, 0)))
    }

    fun getScanResultCount(): Int {
        return ScanResultsHolder.getResultCount()
    }

    fun hasScanResults(): Boolean {
        return ScanResultsHolder.hasResults()
    }

    fun getLatestScanResult(): String? {
        return ScanResultsHolder.getLatestResult()
    }

    fun isCurrentlyScanning(): Boolean {
        return isScanningEnabled.value ?: false
    }

    @OptIn(ExperimentalGetImage::class)
    override fun onCleared() {
        super.onCleared()
        cameraManager.shutdown()
    }
}